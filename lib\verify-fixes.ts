"use client"

/**
 * Verification script to test the critical fixes for runtime errors
 * Run this to verify that both issues have been resolved
 */

import { syncService } from "./sync"
import { db } from "./db"
import { dataClient } from "./data-client"

export class FixVerification {
  
  /**
   * Test Fix 1: SyncService property conflict resolution
   */
  async testSyncServicePropertyFix(): Promise<boolean> {
    try {
      console.log("🔍 Testing SyncService property conflict fix...")
      
      // This should not throw "Cannot set property isOnline" error
      const isOnline = syncService.isOnline
      const isSyncing = syncService.isSyncing
      
      console.log(`✅ SyncService properties accessible: online=${isOnline}, syncing=${isSyncing}`)
      return true
      
    } catch (error) {
      console.error("❌ SyncService property conflict still exists:", error)
      return false
    }
  }

  /**
   * Test Fix 2: Dexie query fixes for dirty/deleted columns
   */
  async testDexieQueryFixes(): Promise<boolean> {
    try {
      console.log("🔍 Testing Dexie query fixes...")
      
      // Test helper methods that previously failed
      const dirtyRecords = await db.getDirtyRecords('patients')
      console.log(`✅ getDirtyRecords works: found ${dirtyRecords.length} dirty patients`)
      
      const deletedRecords = await db.getDeletedRecords('patients')
      console.log(`✅ getDeletedRecords works: found ${deletedRecords.length} deleted patients`)
      
      const unitTypes = await db.getUnitTypes()
      console.log(`✅ getUnitTypes works: found ${unitTypes.length} unit types`)
      
      // Test data client methods
      const patients = await dataClient.getPatients()
      console.log(`✅ getPatients works: found ${patients.length} patients`)
      
      const medicationNames = await dataClient.getMedicationNames()
      console.log(`✅ getMedicationNames works: found ${medicationNames.length} medications`)
      
      return true
      
    } catch (error) {
      console.error("❌ Dexie query fixes failed:", error)
      return false
    }
  }

  /**
   * Test database migration to version 7
   */
  async testDatabaseMigration(): Promise<boolean> {
    try {
      console.log("🔍 Testing database migration...")
      
      // Check if we're on the correct version
      const version = db.verno
      console.log(`Database version: ${version}`)
      
      if (version < 7) {
        console.log("⚠️ Database not yet migrated to version 7")
        return false
      }
      
      // Test that sync status table exists and is populated
      const syncStatus = await dataClient.getSyncStatus()
      console.log(`✅ Sync status table works: ${syncStatus.length} table statuses`)
      
      // Test that new columns exist by creating a test record
      const testPatient = await dataClient.insertPatient({
        patient_id: "VERIFY_TEST",
        name: "Verification Test Patient",
        gender: "male",
        age: 30,
        weight: 70,
        admission_date: new Date().toISOString().split('T')[0],
        diseases: []
      })
      
      // Verify the record has the new sync columns
      if (testPatient.dirty === undefined || testPatient.deleted === undefined || testPatient.updated_at === undefined) {
        throw new Error("New sync columns missing from created record")
      }
      
      console.log(`✅ New sync columns present: dirty=${testPatient.dirty}, deleted=${testPatient.deleted}`)
      
      // Clean up test record
      await dataClient.deletePatient(testPatient.id)
      
      return true
      
    } catch (error) {
      console.error("❌ Database migration test failed:", error)
      return false
    }
  }

  /**
   * Test basic sync functionality
   */
  async testBasicSyncFunctionality(): Promise<boolean> {
    try {
      console.log("🔍 Testing basic sync functionality...")
      
      // Test dirty records counting
      const dirtyCount = await dataClient.getDirtyRecordsCount()
      console.log(`✅ Dirty records count: ${dirtyCount}`)
      
      // Test sync status retrieval
      const syncStatus = await syncService.getSyncStatus()
      console.log(`✅ Sync status retrieval: ${syncStatus.length} tables`)
      
      // Test that sync engine is accessible
      const { syncEngine } = await import('./sync-engine')
      const engineOnline = syncEngine.online
      const engineSyncing = syncEngine.syncing
      console.log(`✅ Sync engine accessible: online=${engineOnline}, syncing=${engineSyncing}`)
      
      return true
      
    } catch (error) {
      console.error("❌ Basic sync functionality test failed:", error)
      return false
    }
  }

  /**
   * Run all verification tests
   */
  async runAllVerifications(): Promise<boolean> {
    console.log("🧪 Starting fix verification tests...")
    console.log("=".repeat(50))
    
    const tests = [
      { name: "SyncService Property Fix", test: () => this.testSyncServicePropertyFix() },
      { name: "Dexie Query Fixes", test: () => this.testDexieQueryFixes() },
      { name: "Database Migration", test: () => this.testDatabaseMigration() },
      { name: "Basic Sync Functionality", test: () => this.testBasicSyncFunctionality() },
    ]
    
    const results = []
    
    for (const { name, test } of tests) {
      console.log(`\n🔬 Running: ${name}`)
      try {
        const result = await test()
        results.push(result)
        console.log(result ? `✅ ${name}: PASSED` : `❌ ${name}: FAILED`)
      } catch (error) {
        console.error(`💥 ${name}: ERROR -`, error)
        results.push(false)
      }
    }
    
    const allPassed = results.every(result => result === true)
    const passedCount = results.filter(r => r).length
    const totalCount = results.length
    
    console.log("\n" + "=".repeat(50))
    console.log(`📊 Verification Results: ${passedCount}/${totalCount} tests passed`)
    
    if (allPassed) {
      console.log("🎉 All fixes verified successfully! The app should now run without runtime errors.")
    } else {
      console.log("⚠️ Some tests failed. Please check the errors above and ensure all fixes are properly applied.")
    }
    
    return allPassed
  }
}

// Export singleton for easy use
export const fixVerification = new FixVerification()

// Auto-run verification in development
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  // Uncomment to auto-run verification on page load
  // setTimeout(() => fixVerification.runAllVerifications(), 3000)
}
