# 🚀 Offline-First Sync Architecture - Setup & Testing Guide

## Overview
This guide provides step-by-step instructions for running the Next.js doctor handovers app with the new robust offline-first sync architecture.

## 🔧 Prerequisites

1. **Node.js** (v18 or higher)
2. **npm/pnpm** package manager
3. **Supabase account** with database access
4. **Modern browser** with IndexedDB support

## 📦 Installation & Setup

### Step 1: Install Dependencies
```bash
# Install all required packages
npm install
# or
pnpm install
```

### Step 2: Database Setup

#### A. Supabase Database Schema
1. **Run the updated SQL schema** in your Supabase SQL editor:
   ```bash
   # Execute the contents of scripts/database-setup.sql in Supabase
   ```
   This will:
   - Add standardized columns (`updated_at`, `dirty`, `deleted`) to all tables
   - Create automatic triggers for timestamp updates
   - Initialize sync metadata table
   - Set up proper indexes and constraints

#### B. Environment Configuration
1. **Update your `.env.local`** with Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

### Step 3: Local Database Migration
The local Dexie database will automatically migrate to version 7 when you first run the app. This migration:
- Adds `updated_at`, `dirty`, `deleted` columns to all tables
- Sets default values for existing records
- Initializes sync status tracking

## 🏃‍♂️ Running the Application

### Step 1: Start Development Server
```bash
npm run dev
# or
pnpm dev
```

### Step 2: Verify Database Migration
1. **Open browser console** and look for migration messages:
   ```
   ✅ Migrated X records in patients
   ✅ Migrated X records in vital_signs
   ...
   🎉 Database migration to version 7 completed successfully
   ```

2. **If migration fails**, check console for specific errors and ensure:
   - Browser supports IndexedDB
   - No other tabs have the app open (IndexedDB locks)
   - Clear browser data if needed: `Application > Storage > Clear storage`

### Step 3: Test Basic Functionality
1. **Navigate to the app** (usually `http://localhost:3000`)
2. **Try basic operations**:
   - Create a patient
   - Add vital signs
   - Add medications
   - Verify data persists after page refresh

## 🧪 Testing the Sync Architecture

### Automated Testing
1. **Visit the test page**: `http://localhost:3000/sync-test`
2. **Run architecture tests**:
   - Click "Run Architecture Tests"
   - Check console output for test results
   - All tests should pass (✅)

### Manual Testing Scenarios

#### Scenario 1: Offline-First Operations
1. **Go offline** (disable network in DevTools)
2. **Perform CRUD operations**:
   - Create patients, notes, medications
   - Update existing records
   - Delete records (soft deletion)
3. **Verify dirty flag tracking**:
   - Check "Dirty Records" count in sync panel
   - Should increase with each operation

#### Scenario 2: Online Sync
1. **Go back online**
2. **Trigger manual sync**:
   - Click "Trigger Manual Sync" in test panel
   - Watch sync results in real-time
3. **Verify sync completion**:
   - Dirty records count should decrease
   - Check "Last Sync Results" for details

#### Scenario 3: Conflict Resolution
1. **Create same record in two browser tabs**
2. **Modify differently in each tab**
3. **Sync both tabs**
4. **Verify last-write-wins resolution**

## 🔍 Monitoring & Debugging

### Sync Status Monitoring
- **Use the sync test panel** at `/sync-test`
- **Monitor sync status** per table
- **Check dirty records count**
- **View detailed sync results**

### Console Logging
The app provides detailed console logging:
```javascript
// Migration logs
✅ Migrated 5 records in patients
🎉 Database migration to version 7 completed successfully

// Sync logs
🧪 Starting sync architecture tests...
✅ CRUD operations test passed
✅ Sync status test passed

// Error logs
❌ Sync failed: Network error
💥 Database migration failed: ...
```

### Common Issues & Solutions

#### Issue: "Cannot set property isOnline"
**Solution**: Fixed in the updated code. The property conflict has been resolved.

#### Issue: "Failed to execute 'bound' on 'IDBKeyRange'"
**Solution**: Fixed by using `.filter()` instead of `.where()` for non-indexed columns.

#### Issue: Migration fails
**Solutions**:
1. Clear browser storage: `DevTools > Application > Storage > Clear storage`
2. Close all other tabs with the app
3. Refresh and try again
4. Check console for specific error details

#### Issue: Sync not working
**Solutions**:
1. Verify internet connection
2. Check Supabase credentials in `.env.local`
3. Ensure Supabase database schema is updated
4. Check browser console for sync errors

## 🏗️ Building for Production

### Step 1: Build the Application
```bash
npm run build
# or
pnpm build
```

### Step 2: Test Production Build
```bash
npm run start
# or
pnpm start
```

### Step 3: Verify Production Functionality
1. **Test offline capabilities**
2. **Verify sync works in production**
3. **Check performance metrics**

## 📊 Architecture Overview

### Key Components
- **Local Database**: Dexie (IndexedDB) with standardized schema
- **Remote Database**: Supabase with mirrored schema
- **Sync Engine**: Bidirectional sync with conflict resolution
- **Data Client**: CRUD operations with automatic dirty flagging
- **Sync Orchestrator**: Manages sync timing and coordination

### Data Flow
1. **Offline**: All operations → Local Database (marked dirty)
2. **Online**: Sync Engine → Push dirty records → Pull updates → Resolve conflicts
3. **Cleanup**: Remove synced deleted records

### Sync Strategy
- **Push Phase**: Upload dirty local records
- **Pull Phase**: Download remote updates
- **Conflict Resolution**: Last-write-wins using timestamps
- **Cleanup**: Purge synced deleted records

## 🎯 Success Criteria

Your app is working correctly when:
- ✅ Database migration completes without errors
- ✅ All architecture tests pass
- ✅ CRUD operations work offline
- ✅ Sync works when online
- ✅ Dirty records are properly tracked
- ✅ Conflicts are resolved automatically
- ✅ Deleted records are cleaned up after sync

## 🆘 Getting Help

If you encounter issues:
1. **Check this guide** for common solutions
2. **Review console logs** for specific errors
3. **Use the sync test panel** for debugging
4. **Verify database schemas** match between local and remote
5. **Test with fresh browser profile** if needed

## 🎉 Congratulations!

You now have a fully functional offline-first app with robust bidirectional sync capabilities!
