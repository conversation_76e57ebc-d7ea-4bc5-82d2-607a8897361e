"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { syncService } from "@/lib/sync"
import { syncArchitectureTest } from "@/lib/sync-test"
import { fixVerification } from "@/lib/verify-fixes"
import { dataClient } from "@/lib/data-client"
import { useOnlineStatus } from "@/hooks/use-online-status"
import { useOutboxCount } from "@/hooks/use-outbox-count"
import type { SyncStatus } from "@/lib/types"
import type { TableSyncResult } from "@/lib/sync-engine"

export function SyncTestPanel() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus[]>([])
  const [testResults, setTestResults] = useState<string[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)
  const [isSyncing, setIsSyncing] = useState(false)
  const [syncResults, setSyncResults] = useState<TableSyncResult[]>([])
  const [dirtyCount, setDirtyCount] = useState(0)

  const isOnline = useOnlineStatus()
  const outboxCount = useOutboxCount() // This now returns dirty records count

  // Load sync status on mount
  useEffect(() => {
    loadSyncStatus()
    loadDirtyCount()
  }, [])

  const loadSyncStatus = async () => {
    try {
      const status = await syncService.getSyncStatus()
      setSyncStatus(status)
    } catch (error) {
      console.error("Failed to load sync status:", error)
    }
  }

  const loadDirtyCount = async () => {
    try {
      const count = await dataClient.getDirtyRecordsCount()
      setDirtyCount(count)
    } catch (error) {
      console.error("Failed to load dirty count:", error)
    }
  }

  const runTests = async () => {
    setIsRunningTests(true)
    setTestResults([])

    try {
      const results: string[] = []

      // Capture console output
      const originalLog = console.log
      const originalError = console.error

      console.log = (...args) => {
        results.push(`LOG: ${args.join(' ')}`)
        originalLog(...args)
      }

      console.error = (...args) => {
        results.push(`ERROR: ${args.join(' ')}`)
        originalError(...args)
      }

      // First run fix verification
      await fixVerification.runAllVerifications()

      // Then run architecture tests
      await syncArchitectureTest.runAllTests()
      await syncArchitectureTest.cleanup()

      // Restore console
      console.log = originalLog
      console.error = originalError

      setTestResults(results)
      await loadSyncStatus()
      await loadDirtyCount()

    } catch (error) {
      setTestResults(prev => [...prev, `FATAL ERROR: ${error}`])
    } finally {
      setIsRunningTests(false)
    }
  }

  const runFixVerification = async () => {
    setIsRunningTests(true)
    setTestResults([])

    try {
      const results: string[] = []

      // Capture console output
      const originalLog = console.log
      const originalError = console.error

      console.log = (...args) => {
        results.push(`LOG: ${args.join(' ')}`)
        originalLog(...args)
      }

      console.error = (...args) => {
        results.push(`ERROR: ${args.join(' ')}`)
        originalError(...args)
      }

      await fixVerification.runAllVerifications()

      // Restore console
      console.log = originalLog
      console.error = originalError

      setTestResults(results)

    } catch (error) {
      setTestResults(prev => [...prev, `FATAL ERROR: ${error}`])
    } finally {
      setIsRunningTests(false)
    }
  }

  const triggerSync = async () => {
    if (!isOnline) {
      setTestResults(prev => [...prev, "Cannot sync: Device is offline"])
      return
    }

    setIsSyncing(true)
    setSyncResults([])

    try {
      const results = await syncService.syncAll()
      setSyncResults(results)
      await loadSyncStatus()
      await loadDirtyCount()
      
      const successCount = results.filter(r => r.result.success).length
      const totalCount = results.length
      
      setTestResults(prev => [...prev, `Sync completed: ${successCount}/${totalCount} tables synced successfully`])

    } catch (error) {
      setTestResults(prev => [...prev, `Sync failed: ${error}`])
    } finally {
      setIsSyncing(false)
    }
  }

  const clearResults = () => {
    setTestResults([])
    setSyncResults([])
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Sync Architecture Test Panel</CardTitle>
          <CardDescription>
            Test and monitor the new offline-first bidirectional sync system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status Indicators */}
          <div className="flex gap-4 flex-wrap">
            <Badge variant={isOnline ? "default" : "destructive"}>
              {isOnline ? "Online" : "Offline"}
            </Badge>
            <Badge variant={isSyncing ? "secondary" : "outline"}>
              {isSyncing ? "Syncing..." : "Idle"}
            </Badge>
            <Badge variant={dirtyCount > 0 ? "secondary" : "outline"}>
              {dirtyCount} Dirty Records
            </Badge>
            <Badge variant="outline">
              {outboxCount} Outbox Count (Legacy)
            </Badge>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={runFixVerification}
              disabled={isRunningTests}
              variant="default"
            >
              {isRunningTests ? "Running..." : "Verify Fixes"}
            </Button>
            <Button
              onClick={runTests}
              disabled={isRunningTests}
              variant="secondary"
            >
              {isRunningTests ? "Running Tests..." : "Run Full Tests"}
            </Button>
            <Button 
              onClick={triggerSync} 
              disabled={isSyncing || !isOnline}
              variant="secondary"
            >
              {isSyncing ? "Syncing..." : "Trigger Manual Sync"}
            </Button>
            <Button 
              onClick={loadSyncStatus} 
              variant="outline"
            >
              Refresh Status
            </Button>
            <Button 
              onClick={clearResults} 
              variant="ghost"
            >
              Clear Results
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sync Status */}
      {syncStatus.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Sync Status by Table</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              {syncStatus.map((status) => (
                <div key={status.tableName} className="flex justify-between items-center p-2 border rounded">
                  <span className="font-medium">{status.tableName}</span>
                  <div className="flex gap-2 items-center">
                    <Badge variant={status.isSyncing ? "secondary" : "outline"}>
                      {status.isSyncing ? "Syncing" : "Idle"}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {status.lastSyncAt ? new Date(status.lastSyncAt).toLocaleTimeString() : "Never"}
                    </span>
                    {status.error && (
                      <Badge variant="destructive" className="text-xs">
                        Error
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sync Results */}
      {syncResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Last Sync Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {syncResults.map((result, index) => (
                <div key={index} className="p-3 border rounded">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">{result.tableName}</span>
                    <Badge variant={result.result.success ? "default" : "destructive"}>
                      {result.result.success ? "Success" : "Failed"}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground grid grid-cols-2 gap-2">
                    <span>Pushed: {result.result.pushedCount}</span>
                    <span>Pulled: {result.result.pulledCount}</span>
                    <span>Conflicts: {result.result.conflictsResolved}</span>
                    <span>Deleted: {result.result.deletedCount}</span>
                  </div>
                  {result.result.errors.length > 0 && (
                    <div className="mt-2 text-sm text-destructive">
                      Errors: {result.result.errors.join(", ")}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1 font-mono text-sm max-h-96 overflow-y-auto">
              {testResults.map((result, index) => (
                <div 
                  key={index} 
                  className={`p-1 ${
                    result.includes('ERROR') || result.includes('❌') 
                      ? 'text-destructive' 
                      : result.includes('✅') 
                      ? 'text-green-600' 
                      : 'text-muted-foreground'
                  }`}
                >
                  {result}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
