"use client"

import { db } from "./db"
import { supabase } from "./supabase"
import { syncEngine, syncOrchestrator, type TableSyncResult } from "./sync-engine"
import type { SyncStatus } from "./types"

/**
 * Enhanced sync service with bidirectional sync capabilities
 * Replaces the old outbox-based sync with a robust offline-first architecture
 */
class SyncService {
  private _isOnline = true // Start with true to avoid hydration mismatch
  private isInitialized = false

  constructor() {
    // Defer initialization to prevent hydration issues
    if (typeof window !== "undefined") {
      this.initialize()
    }
  }

  private initialize() {
    if (this.isInitialized) return
    this.isInitialized = true

    this._isOnline = navigator.onLine

    // The sync orchestrator handles online/offline events and periodic sync
    // No need to duplicate that logic here
  }

  /**
   * Start periodic sync (delegates to sync orchestrator)
   */
  startPeriodicSync() {
    syncOrchestrator.startPeriodicSync()
  }

  /**
   * Stop periodic sync (delegates to sync orchestrator)
   */
  stopPeriodicSync() {
    syncOrchestrator.stopPeriodicSync()
  }

  /**
   * Trigger bidirectional sync for all tables
   */
  async syncAll(): Promise<TableSyncResult[]> {
    return await syncOrchestrator.manualSync()
  }

  /**
   * Legacy method name for backward compatibility
   */
  async syncOutbox(): Promise<TableSyncResult[]> {
    return await this.syncAll()
  }

  /**
   * Get sync status for all tables
   */
  async getSyncStatus(): Promise<SyncStatus[]> {
    const { SYNC_CONFIG } = await import('./types')
    const statusList: SyncStatus[] = []

    for (const config of SYNC_CONFIG) {
      const status = await db.getSyncStatus(config.tableName)
      if (status) {
        statusList.push(status)
      }
    }

    return statusList
  }

  /**
   * Get sync status for a specific table
   */
  async getSyncStatusForTable(tableName: string): Promise<SyncStatus | undefined> {
    return await db.getSyncStatus(tableName)
  }

  /**
   * Check if any sync is currently in progress
   */
  get isSyncing(): boolean {
    return syncEngine.syncing
  }

  /**
   * Check if device is online
   */
  get isOnline(): boolean {
    return this._isOnline
  }

  /**
   * Pull server data for all tables (enhanced version with conflict resolution)
   */
  async pullServerData(): Promise<TableSyncResult[]> {
    if (!this.isOnline) {
      return []
    }

    return await this.syncAll()
  }

  /**
   * Sync specific tables (enhanced version with bidirectional sync)
   */
  async syncTables(tableNames: string[]): Promise<TableSyncResult[]> {
    if (!this.isOnline) return []

    const results: TableSyncResult[] = []

    for (const tableName of tableNames) {
      try {
        const result = await syncEngine.syncTable(tableName as any)
        results.push({
          tableName,
          result
        })
      } catch (error) {
        results.push({
          tableName,
          result: {
            success: false,
            pushedCount: 0,
            pulledCount: 0,
            conflictsResolved: 0,
            deletedCount: 0,
            errors: [error instanceof Error ? error.message : "Unknown error"]
          }
        })
      }
    }

    return results
  }

  /**
   * Sync medication-related tables with enhanced bidirectional sync
   */
  async syncMedications(): Promise<TableSyncResult[]> {
    const medicationTables = ["medications", "medication_names", "medication_adherence"]
    return await this.syncTables(medicationTables)
  }

  /**
   * Ensure medications are synced once per browser session (enhanced version)
   */
  async ensureMedicationsSyncedThisSession(): Promise<TableSyncResult[]> {
    try {
      if (typeof window === "undefined") return []

      const flag = sessionStorage.getItem("medications_sync_done")
      if (flag) return []

      const results = await this.syncMedications()

      // Mark session-level completion if sync was successful
      const allSuccessful = results.every(r => r.result.success)
      if (allSuccessful) {
        sessionStorage.setItem("medications_sync_done", new Date().toISOString())
      }

      return results
    } catch (error) {
      throw error
    }
  }
}

export const syncService = new SyncService()
