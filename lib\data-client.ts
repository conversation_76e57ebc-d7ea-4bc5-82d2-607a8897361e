"use client"

import { db } from "./db"
import { supabase } from "./supabase"
import { syncOrchestrator } from "./sync-engine"
import { v4 as uuidv4 } from "uuid"
import type { Patient, VitalSigns, LabValues, Medication, DoctorNote, Culture, Radiology, MedicationAdherence, UnitType } from "./types"
import { GENDER_TYPES, CULTURE_STATUSES, RADI<PERSON>OGY_SCAN_TYPES, RADIOLOGY_STATUSES, USER_ROLES } from "./constants"

class DataClient {
  // Reference data methods (now return hardcoded constants)
  getUserRoles() {
    return [...USER_ROLES]
  }

  getGenderTypes() {
    return [...GENDER_TYPES]
  }

  async getUnitTypes(isOnline: boolean = false): Promise<UnitType[]> {
    if (isOnline) {
      try {
        // Fetch from online database
        const { data: onlineUnits, error } = await supabase
          .from('unit_types')
          .select('id, name')
          .order('name')

        if (error) {
          console.error('Error fetching units from online DB:', error)
          // Fallback to local DB
          return await db.getUnitTypes()
        }

        if (onlineUnits && onlineUnits.length > 0) {
          // Transform online data to match UnitType interface
          const now = new Date().toISOString()
          const transformedUnits: UnitType[] = onlineUnits.map(unit => ({
            ...unit,
            is_active: true,
            created_at: now
          }))
          
          // Update local database with online data
          await db.unit_types.clear()
          await db.unit_types.bulkAdd(transformedUnits)
          return transformedUnits
        }
      } catch (error) {
        console.error('Error in online unit fetch:', error)
      }
    }
    
    // Return from local database (offline mode or online fetch failed)
    return await db.getUnitTypes()
  }

  getCultureStatuses() {
    return [...CULTURE_STATUSES]
  }

  getRadiologyScanTypes() {
    return [...RADIOLOGY_SCAN_TYPES]
  }

  getRadiologyStatuses() {
    return [...RADIOLOGY_STATUSES]
  }

  // Helper method for unit lookup (still needed)
  async getUnitIdByName(name: string): Promise<number | undefined> {
    const unit = await db.unit_types.where('name').equals(name).first()
    return unit?.id
  }

  // Helper method to trigger sync after changes
  private triggerSyncAfterChange() {
    syncOrchestrator.triggerSyncAfterChange()
  }

  // Patients
  async insertPatient(patient: Omit<Patient, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    const id = uuidv4()
    const now = new Date().toISOString()
    const newPatient: Patient = {
      ...patient,
      id,
      created_at: now,
      updated_at: now,
      dirty: true, // Mark as dirty for sync
      deleted: false,
    }

    await db.patients.add(newPatient)
    this.triggerSyncAfterChange()
    return newPatient
  }

  async updatePatient(id: string, updates: Partial<Patient>) {
    const now = new Date().toISOString()
    const updatedData = {
      ...updates,
      updated_at: now,
      dirty: true // Mark as dirty for sync
    }
    await db.patients.update(id, updatedData)
    this.triggerSyncAfterChange()
    return updatedData
  }

  async deletePatient(id: string) {
    await db.transaction(
      "rw",
      [
        db.patients,
        db.vital_signs,
        db.lab_values,
        db.medications,
        db.medication_adherence,
        db.doctor_notes,
        db.cultures,
        db.radiology,
      ],
      async () => {
        const patient = await db.patients.get(id)
        if (!patient) return

        const patientId = patient.patient_id
        const now = new Date().toISOString()

        // Soft delete all related records
        const relatedTables = [
          { table: db.vital_signs, field: "patient_id" },
          { table: db.lab_values, field: "patient_id" },
          { table: db.medications, field: "patient_id" },
          { table: db.medication_adherence, field: "patient_id" },
          { table: db.doctor_notes, field: "patient_id" },
          { table: db.cultures, field: "patient_id" },
          { table: db.radiology, field: "patient_id" },
        ]

        for (const { table, field } of relatedTables) {
          const records = await table.where(field).equals(patientId).toArray()
          for (const record of records) {
            await table.update(record.id, {
              deleted: true,
              dirty: true,
              updated_at: now
            })
          }
        }

        // Soft delete the patient
        await db.patients.update(id, {
          deleted: true,
          dirty: true,
          updated_at: now
        })
      },
    )

    this.triggerSyncAfterChange()
  }

  async getPatients(
    status?: "active" | "discharged" | "deceased",
  ): Promise<Patient[]> {
    // Only get non-deleted patients
    const patients = await db.patients.filter(record => !record.deleted).toArray()

    // Fetch unit reference data for joining (only unit_types still exists as table)
    const unitTypes = await db.getUnitTypes()
    const unitMap = new Map(unitTypes.map(u => [u.id, u]))

    let filteredPatients = patients
    if (status === "active") {
      filteredPatients = patients.filter(
        (p) => (p.is_discharged ?? false) === false && (p.is_deceased ?? false) === false,
      )
    } else if (status === "discharged") {
      filteredPatients = patients.filter((p) => (p.is_discharged ?? false) === true)
    } else if (status === "deceased") {
      filteredPatients = patients.filter((p) => (p.is_deceased ?? false) === true)
    }

    // Add joined unit data (gender is now direct string value)
    const enrichedPatients = filteredPatients.map(patient => ({
      ...patient,
      unit: patient.unit_id ? unitMap.get(patient.unit_id) : undefined
    }))

    return enrichedPatients.sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    )
  }

  async getPatient(id: string): Promise<Patient | undefined> {
    return await db.patients.get(id)
  }

  async getPatientByPatientId(patientId: string): Promise<Patient | undefined> {
    return await db.patients.where("patient_id").equals(patientId).first()
  }

  // Vital Signs
  async upsertVitalSigns(vitalSigns: Omit<VitalSigns, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    // Check if a record exists for this patient and date using the compound index
    const existing = await db.vital_signs
      .where("[patient_id+date]")
      .equals([vitalSigns.patient_id, vitalSigns.date])
      .filter(record => !record.deleted)
      .first()

    const now = new Date().toISOString()

    if (existing) {
      const updatedData = {
        ...vitalSigns,
        updated_at: now,
        dirty: true
      }
      await db.vital_signs.update(existing.id, updatedData)
      this.triggerSyncAfterChange()
      return { ...existing, ...updatedData }
    } else {
      const id = uuidv4()
      const newVitalSigns: VitalSigns = {
        ...vitalSigns,
        id,
        created_at: now,
        updated_at: now,
        dirty: true,
        deleted: false,
      }
      await db.vital_signs.add(newVitalSigns)
      this.triggerSyncAfterChange()
      return newVitalSigns
    }
  }

  async getVitalSigns(patientId: string): Promise<VitalSigns[]> {
    const vitals = await db.vital_signs
      .where("patient_id").equals(patientId)
      .filter(record => !record.deleted)
      .toArray()
    return vitals.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  // Lab Values
  async upsertLabValues(labValues: Omit<LabValues, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    // Check if a record exists for this patient and date using the compound index
    const existing = await db.lab_values
      .where("[patient_id+date]")
      .equals([labValues.patient_id, labValues.date])
      .filter(record => !record.deleted)
      .first()

    const now = new Date().toISOString()

    if (existing) {
      const updatedData = {
        ...labValues,
        updated_at: now,
        dirty: true
      }
      await db.lab_values.update(existing.id, updatedData)
      this.triggerSyncAfterChange()
      return { ...existing, ...updatedData }
    } else {
      const id = uuidv4()
      const newLabValues: LabValues = {
        ...labValues,
        id,
        created_at: now,
        updated_at: now,
        dirty: true,
        deleted: false,
      }
      await db.lab_values.add(newLabValues)
      this.triggerSyncAfterChange()
      return newLabValues
    }
  }

  async getLabValues(patientId: string): Promise<LabValues[]> {
    const labs = await db.lab_values
      .where("patient_id").equals(patientId)
      .filter(record => !record.deleted)
      .toArray()
    return labs.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  // Medications
  async insertMedication(medication: Omit<Medication, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    const id = uuidv4()
    const now = new Date().toISOString()
    const newMedication: Medication = {
      ...medication,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false,
    }

    await db.medications.add(newMedication)
    this.triggerSyncAfterChange()
    return newMedication
  }

  async updateMedication(id: string, updates: Partial<Medication>) {
    const now = new Date().toISOString()
    const updatedData = {
      ...updates,
      updated_at: now,
      dirty: true
    }
    await db.medications.update(id, updatedData)
    this.triggerSyncAfterChange()
  }

  async deleteMedication(id: string) {
    const now = new Date().toISOString()
    await db.medications.update(id, {
      deleted: true,
      dirty: true,
      updated_at: now
    })
    this.triggerSyncAfterChange()
  }

  async getMedications(patientId: string): Promise<Medication[]> {
    const medications = await db.medications
      .where("patient_id").equals(patientId)
      .filter(record => !record.deleted)
      .toArray()
    return medications.sort((a, b) => new Date(b.date_prescribed).getTime() - new Date(a.date_prescribed).getTime())
  }

  // Medication adherence
  async upsertMedicationAdherence(record: Omit<MedicationAdherence, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    // Compound unique key [patient_id+medication_id+date]
    const existing = await db.medication_adherence
      .where("[patient_id+medication_id+date]")
      .equals([record.patient_id, record.medication_id, record.date])
      .filter(rec => !rec.deleted)
      .first()

    const now = new Date().toISOString()

    if (existing) {
      const updatedData = {
        ...record,
        updated_at: now,
        dirty: true
      }
      await db.medication_adherence.update(existing.id, updatedData)
      this.triggerSyncAfterChange()
      return { ...existing, ...updatedData }
    } else {
      const id = uuidv4()
      const newRecord: MedicationAdherence = {
        ...record,
        id,
        created_at: now,
        updated_at: now,
        dirty: true,
        deleted: false,
      }
      await db.medication_adherence.add(newRecord)
      this.triggerSyncAfterChange()
      return newRecord
    }
  }

  async getMedicationAdherenceByPatient(patientId: string): Promise<MedicationAdherence[]> {
    const rows = await db.medication_adherence
      .where("patient_id").equals(patientId)
      .filter(record => !record.deleted)
      .toArray()
    return rows.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }

  // Medication Names
  async insertMedicationName(name: string) {
    const id = uuidv4()
    const now = new Date().toISOString()
    const medicationName: MedicationName = {
      id,
      name: name.toLowerCase(),
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false,
    }

    await db.medication_names.add(medicationName)
    this.triggerSyncAfterChange()
    return medicationName
  }

  async getMedicationNames(): Promise<string[]> {
    const names = await db.medication_names
      .filter(record => !record.deleted)
      .toArray()
    // Sort manually since we can't use orderBy with filter
    names.sort((a, b) => a.name.localeCompare(b.name))
    return names.map((n) => n.name)
  }

  // Doctor Notes
  async insertDoctorNote(note: Omit<DoctorNote, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    const id = uuidv4()
    const now = new Date().toISOString()
    const newNote: DoctorNote = {
      ...note,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false,
    }

    await db.doctor_notes.add(newNote)
    this.triggerSyncAfterChange()
    return newNote
  }

  async updateDoctorNote(id: string, updates: Partial<DoctorNote>) {
    const now = new Date().toISOString()
    const updatedData = {
      ...updates,
      updated_at: now,
      dirty: true
    }
    await db.doctor_notes.update(id, updatedData)
    this.triggerSyncAfterChange()
  }

  async deleteDoctorNote(id: string) {
    const now = new Date().toISOString()
    await db.doctor_notes.update(id, {
      deleted: true,
      dirty: true,
      updated_at: now
    })
    this.triggerSyncAfterChange()
  }

  async getDoctorNotes(patientId: string): Promise<DoctorNote[]> {
    const notes = await db.doctor_notes
      .where("patient_id").equals(patientId)
      .filter(record => !record.deleted)
      .toArray()
    return notes.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  // Cultures
  async insertCulture(culture: Omit<Culture, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    const id = uuidv4()
    const now = new Date().toISOString()
    const newCulture: Culture = {
      ...culture,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false,
    }

    await db.cultures.add(newCulture)
    this.triggerSyncAfterChange()
    return newCulture
  }

  async updateCulture(id: string, updates: Partial<Culture>) {
    const now = new Date().toISOString()
    const updatedData = {
      ...updates,
      updated_at: now,
      dirty: true
    }
    await db.cultures.update(id, updatedData)
    this.triggerSyncAfterChange()
  }

  async deleteCulture(id: string) {
    const now = new Date().toISOString()
    await db.cultures.update(id, {
      deleted: true,
      dirty: true,
      updated_at: now
    })
    this.triggerSyncAfterChange()
  }

  async getCultures(patientId: string): Promise<Culture[]> {
    const cultures = await db.cultures
      .where("patient_id").equals(patientId)
      .filter(record => !record.deleted)
      .toArray()

    // No need to join - status is now a direct string value
    return cultures.sort((a, b) => new Date(b.requested_date).getTime() - new Date(a.requested_date).getTime())
  }

  // Radiology
  async insertRadiology(radiology: Omit<Radiology, "id" | "created_at" | "updated_at" | "dirty" | "deleted">) {
    const id = uuidv4()
    const now = new Date().toISOString()
    const newRadiology: Radiology = {
      ...radiology,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false,
    }

    await db.radiology.add(newRadiology)
    this.triggerSyncAfterChange()
    return newRadiology
  }

  async updateRadiology(id: string, updates: Partial<Radiology>) {
    const now = new Date().toISOString()
    const updatedData = {
      ...updates,
      updated_at: now,
      dirty: true
    }
    await db.radiology.update(id, updatedData)
    this.triggerSyncAfterChange()
  }

  async deleteRadiology(id: string) {
    const now = new Date().toISOString()
    await db.radiology.update(id, {
      deleted: true,
      dirty: true,
      updated_at: now
    })
    this.triggerSyncAfterChange()
  }

  async getRadiology(patientId: string): Promise<Radiology[]> {
    const radiology = await db.radiology
      .where("patient_id").equals(patientId)
      .filter(record => !record.deleted)
      .toArray()

    // No need to join - scan_type and status are now direct string values
    return radiology.sort((a, b) => new Date(b.scan_date).getTime() - new Date(a.scan_date).getTime())
  }

  // Sync status operations (replacing outbox operations)
  async getSyncStatus() {
    return await db.sync_status.toArray()
  }

  async getSyncStatusForTable(tableName: string) {
    return await db.getSyncStatus(tableName)
  }

  async getDirtyRecordsCount(): Promise<number> {
    const { SYNC_CONFIG } = await import('./types')
    let totalCount = 0

    for (const config of SYNC_CONFIG) {
      if (config.enabled) {
        const dirtyRecords = await db.getDirtyRecords(config.tableName)
        totalCount += dirtyRecords.length
      }
    }

    return totalCount
  }

  // Legacy outbox methods for backward compatibility (will be removed after transition)
  async getOutboxCount(): Promise<number> {
    return await this.getDirtyRecordsCount()
  }
}

export const dataClient = new DataClient()
